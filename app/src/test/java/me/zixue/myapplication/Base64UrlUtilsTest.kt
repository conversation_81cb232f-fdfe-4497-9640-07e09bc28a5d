package me.zixue.myapplication

import me.zixue.myapplication.utils.Base64UrlUtils
import org.junit.Test
import org.junit.Assert.*

/**
 * Base64UrlUtils的单元测试
 * 验证URL编码/解码功能的正确性和安全性
 */
class Base64UrlUtilsTest {

    @Test
    fun testEncodeUrl_validHttpUrl() {
        val originalUrl = "https://k.ziyibbs.com/unzipdata.s"
        val encoded = Base64UrlUtils.encodeUrl(originalUrl)
        
        assertNotNull("编码结果不应为null", encoded)
        assertTrue("编码结果不应为空", encoded!!.isNotEmpty())
        
        // 验证编码结果可以正确解码
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        assertEquals("解码结果应与原始URL相同", originalUrl, decoded)
    }

    @Test
    fun testEncodeUrl_validHttpsUrl() {
        val originalUrl = "https://example.com/file.zip"
        val encoded = Base64UrlUtils.encodeUrl(originalUrl)
        
        assertNotNull("编码结果不应为null", encoded)
        assertTrue("编码结果不应为空", encoded!!.isNotEmpty())
        
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        assertEquals("解码结果应与原始URL相同", originalUrl, decoded)
    }

    @Test
    fun testEncodeUrl_validFtpUrl() {
        val originalUrl = "ftp://ftp.example.com/file.zip"
        val encoded = Base64UrlUtils.encodeUrl(originalUrl)
        
        assertNotNull("编码结果不应为null", encoded)
        assertTrue("编码结果不应为空", encoded!!.isNotEmpty())
        
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        assertEquals("解码结果应与原始URL相同", originalUrl, decoded)
    }

    @Test
    fun testEncodeUrl_nullUrl() {
        val encoded = Base64UrlUtils.encodeUrl(null)
        assertNull("null URL的编码结果应为null", encoded)
    }

    @Test
    fun testEncodeUrl_emptyUrl() {
        val encoded = Base64UrlUtils.encodeUrl("")
        assertNull("空URL的编码结果应为null", encoded)
    }

    @Test
    fun testEncodeUrl_blankUrl() {
        val encoded = Base64UrlUtils.encodeUrl("   ")
        assertNull("空白URL的编码结果应为null", encoded)
    }

    @Test
    fun testEncodeUrl_invalidUrl() {
        val invalidUrl = "not-a-valid-url"
        val encoded = Base64UrlUtils.encodeUrl(invalidUrl)
        assertNull("无效URL的编码结果应为null", encoded)
    }

    @Test
    fun testDecodeUrl_validBase64() {
        val originalUrl = "https://k.ziyibbs.com/unzipdata.s"
        val encoded = Base64UrlUtils.encodeUrl(originalUrl)
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        
        assertEquals("解码结果应与原始URL相同", originalUrl, decoded)
    }

    @Test
    fun testDecodeUrl_nullBase64() {
        val decoded = Base64UrlUtils.decodeUrl(null)
        assertNull("null Base64的解码结果应为null", decoded)
    }

    @Test
    fun testDecodeUrl_emptyBase64() {
        val decoded = Base64UrlUtils.decodeUrl("")
        assertNull("空Base64的解码结果应为null", decoded)
    }

    @Test
    fun testDecodeUrl_invalidBase64() {
        val invalidBase64 = "invalid-base64-string!"
        val decoded = Base64UrlUtils.decodeUrl(invalidBase64)
        assertNull("无效Base64的解码结果应为null", decoded)
    }

    @Test
    fun testGetSafeUrl_originalUrl() {
        val originalUrl = "https://example.com/file.zip"
        val result = Base64UrlUtils.getSafeUrl(originalUrl)
        
        assertEquals("原始URL应直接返回", originalUrl, result)
    }

    @Test
    fun testGetSafeUrl_base64Url() {
        val originalUrl = "https://k.ziyibbs.com/unzipdata.s"
        val encoded = Base64UrlUtils.encodeUrl(originalUrl)
        val result = Base64UrlUtils.getSafeUrl(encoded)
        
        assertEquals("Base64编码URL应正确解码", originalUrl, result)
    }

    @Test
    fun testGetSafeUrl_nullInput() {
        val result = Base64UrlUtils.getSafeUrl(null)
        assertNull("null输入应返回null", result)
    }

    @Test
    fun testGetSafeUrl_invalidInput() {
        val invalidInput = "invalid-input-123"
        val result = Base64UrlUtils.getSafeUrl(invalidInput)
        assertNull("无效输入应返回null", result)
    }

    @Test
    fun testGetSafeDisplayUrl_normalUrl() {
        val url = "https://k.ziyibbs.com/unzipdata.s"
        val displayUrl = Base64UrlUtils.getSafeDisplayUrl(url)
        
        assertNotNull("显示URL不应为null", displayUrl)
        assertTrue("显示URL应包含协议和域名", displayUrl.contains("https://k.ziyibbs.com"))
        assertTrue("显示URL应隐藏敏感路径", displayUrl.contains("***"))
    }

    @Test
    fun testGetSafeDisplayUrl_shortUrl() {
        val shortUrl = "https://a.com"
        val displayUrl = Base64UrlUtils.getSafeDisplayUrl(shortUrl)
        
        assertNotNull("显示URL不应为null", displayUrl)
        assertTrue("短URL应显示前几个字符", displayUrl.contains("https://a"))
    }

    @Test
    fun testGetSafeDisplayUrl_nullUrl() {
        val displayUrl = Base64UrlUtils.getSafeDisplayUrl(null)
        assertEquals("null URL应返回特定文本", "空URL", displayUrl)
    }

    @Test
    fun testGetSafeDisplayUrl_emptyUrl() {
        val displayUrl = Base64UrlUtils.getSafeDisplayUrl("")
        assertEquals("空URL应返回特定文本", "空URL", displayUrl)
    }

    @Test
    fun testRoundTripEncoding() {
        val testUrls = listOf(
            "https://k.ziyibbs.com/unzipdata.s",
            "http://example.com/path/to/file.zip",
            "ftp://ftp.example.com/directory/file.tar.gz",
            "https://cdn.example.com/assets/data.json"
        )
        
        for (url in testUrls) {
            val encoded = Base64UrlUtils.encodeUrl(url)
            assertNotNull("URL编码不应失败: $url", encoded)
            
            val decoded = Base64UrlUtils.decodeUrl(encoded)
            assertEquals("往返编码应保持一致: $url", url, decoded)
        }
    }

    @Test
    fun testSpecialCharactersInUrl() {
        val urlWithSpecialChars = "https://example.com/path?param=value&other=123#fragment"
        val encoded = Base64UrlUtils.encodeUrl(urlWithSpecialChars)
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        
        assertEquals("包含特殊字符的URL应正确编码解码", urlWithSpecialChars, decoded)
    }

    @Test
    fun testChineseCharactersInUrl() {
        val urlWithChinese = "https://example.com/文件.zip"
        val encoded = Base64UrlUtils.encodeUrl(urlWithChinese)
        val decoded = Base64UrlUtils.decodeUrl(encoded)
        
        assertEquals("包含中文字符的URL应正确编码解码", urlWithChinese, decoded)
    }
}
