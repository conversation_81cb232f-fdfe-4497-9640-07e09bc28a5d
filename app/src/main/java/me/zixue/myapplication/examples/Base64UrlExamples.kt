package me.zixue.myapplication.examples

import me.zixue.myapplication.utils.Base64UrlUtils
import android.util.Log

/**
 * Base64 URL编码使用示例
 * 展示在不同场景下如何使用Base64编码的fileUrl
 */
object Base64UrlExamples {
    
    private const val TAG = "Base64UrlExamples"
    
    /**
     * 场景1：配置文件中存储编码后的URL
     * 适用于：需要在配置文件中隐藏真实下载地址的场景
     */
    fun demonstrateConfigFileUsage() {
        Log.d(TAG, "=== 场景1：配置文件中存储编码后的URL ===")
        
        val originalUrl = "https://k.ziyibbs.com/unzipdata.s"
        val encodedUrl = Base64UrlUtils.encodeUrl(originalUrl)
        
        Log.d(TAG, "原始URL: ${Base64UrlUtils.getSafeDisplayUrl(originalUrl)}")
        Log.d(TAG, "编码后URL: $encodedUrl")
        Log.d(TAG, "配置文件中可以存储编码后的URL，避免直接暴露下载地址")
        
        // 使用时解码
        val decodedUrl = Base64UrlUtils.getSafeUrl(encodedUrl)
        Log.d(TAG, "使用时解码: ${Base64UrlUtils.getSafeDisplayUrl(decodedUrl)}")
    }
    
    /**
     * 场景2：日志记录中的URL脱敏
     * 适用于：需要在日志中记录URL但不想暴露完整地址的场景
     */
    fun demonstrateLogSafetyUsage() {
        Log.d(TAG, "=== 场景2：日志记录中的URL脱敏 ===")
        
        val sensitiveUrls = listOf(
            "https://private-server.com/secret-file.zip",
            "https://internal.company.com/confidential/data.json",
            "ftp://secure.example.com/private/archive.tar.gz"
        )
        
        for (url in sensitiveUrls) {
            // 在日志中使用安全显示版本
            Log.d(TAG, "处理文件: ${Base64UrlUtils.getSafeDisplayUrl(url)}")
            
            // 实际使用时可以是完整URL或Base64编码
            val encodedForStorage = Base64UrlUtils.encodeUrl(url)
            Log.d(TAG, "存储编码: $encodedForStorage")
        }
    }
    
    /**
     * 场景3：网络传输时的URL混淆
     * 适用于：API调用时需要传递URL但不想直接暴露的场景
     */
    fun demonstrateNetworkTransmissionUsage() {
        Log.d(TAG, "=== 场景3：网络传输时的URL混淆 ===")
        
        val downloadUrl = "https://cdn.example.com/updates/version-2.1.zip"
        
        // 编码用于网络传输
        val encodedForTransmission = Base64UrlUtils.encodeUrl(downloadUrl)
        Log.d(TAG, "网络传输编码: $encodedForTransmission")
        
        // 模拟API响应处理
        val receivedEncodedUrl = encodedForTransmission // 从API接收到的编码URL
        val actualDownloadUrl = Base64UrlUtils.decodeUrl(receivedEncodedUrl)
        
        Log.d(TAG, "解码后用于下载: ${Base64UrlUtils.getSafeDisplayUrl(actualDownloadUrl)}")
    }
    
    /**
     * 场景4：多环境URL管理
     * 适用于：开发、测试、生产环境使用不同URL的场景
     */
    fun demonstrateMultiEnvironmentUsage() {
        Log.d(TAG, "=== 场景4：多环境URL管理 ===")
        
        // 不同环境的URL（实际项目中可能从配置文件读取）
        val environmentUrls = mapOf(
            "development" to "aHR0cDovL2Rldi1zZXJ2ZXIuY29tL2ZpbGUuemlw", // http://dev-server.com/file.zip
            "testing" to "aHR0cHM6Ly90ZXN0LXNlcnZlci5jb20vZmlsZS56aXA=", // https://test-server.com/file.zip
            "production" to "aHR0cHM6Ly9rLnppeWliYnMuY29tL3VuemlwZGF0YS5z" // https://k.ziyibbs.com/unzipdata.s
        )
        
        val currentEnvironment = "production" // 可以从配置获取
        
        val encodedUrl = environmentUrls[currentEnvironment]
        val actualUrl = Base64UrlUtils.getSafeUrl(encodedUrl)
        
        Log.d(TAG, "当前环境: $currentEnvironment")
        Log.d(TAG, "使用URL: ${Base64UrlUtils.getSafeDisplayUrl(actualUrl)}")
    }
    
    /**
     * 场景5：URL有效性验证
     * 适用于：需要验证URL格式和可用性的场景
     */
    fun demonstrateUrlValidationUsage() {
        Log.d(TAG, "=== 场景5：URL有效性验证 ===")
        
        val testInputs = listOf(
            "https://k.ziyibbs.com/unzipdata.s", // 有效的原始URL
            "aHR0cHM6Ly9rLnppeWliYnMuY29tL3VuemlwZGF0YS5z", // 有效的Base64编码URL
            "invalid-url", // 无效输入
            "", // 空输入
            null // null输入
        )
        
        for (input in testInputs) {
            val safeUrl = Base64UrlUtils.getSafeUrl(input)
            if (safeUrl != null) {
                Log.d(TAG, "✓ 有效输入: ${Base64UrlUtils.getSafeDisplayUrl(input)} -> ${Base64UrlUtils.getSafeDisplayUrl(safeUrl)}")
            } else {
                Log.d(TAG, "✗ 无效输入: $input")
            }
        }
    }
    
    /**
     * 场景6：批量URL处理
     * 适用于：需要处理多个下载URL的场景
     */
    fun demonstrateBatchProcessingUsage() {
        Log.d(TAG, "=== 场景6：批量URL处理 ===")
        
        val urlList = listOf(
            "https://server1.com/file1.zip",
            "https://server2.com/file2.zip",
            "ftp://server3.com/file3.tar.gz"
        )
        
        // 批量编码
        val encodedUrls = mutableListOf<String>()
        for (url in urlList) {
            val encoded = Base64UrlUtils.encodeUrl(url)
            if (encoded != null) {
                encodedUrls.add(encoded)
                Log.d(TAG, "编码成功: ${Base64UrlUtils.getSafeDisplayUrl(url)}")
            } else {
                Log.e(TAG, "编码失败: ${Base64UrlUtils.getSafeDisplayUrl(url)}")
            }
        }
        
        Log.d(TAG, "批量编码完成，共处理 ${encodedUrls.size} 个URL")
        
        // 批量解码验证
        var successCount = 0
        for (encodedUrl in encodedUrls) {
            val decoded = Base64UrlUtils.decodeUrl(encodedUrl)
            if (decoded != null) {
                successCount++
                Log.d(TAG, "解码验证成功: ${Base64UrlUtils.getSafeDisplayUrl(decoded)}")
            }
        }
        
        Log.d(TAG, "批量解码验证完成，成功率: $successCount/${encodedUrls.size}")
    }
    
    /**
     * 运行所有示例
     */
    fun runAllExamples() {
        Log.d(TAG, "开始运行Base64 URL编码使用示例...")
        
        demonstrateConfigFileUsage()
        demonstrateLogSafetyUsage()
        demonstrateNetworkTransmissionUsage()
        demonstrateMultiEnvironmentUsage()
        demonstrateUrlValidationUsage()
        demonstrateBatchProcessingUsage()
        
        Log.d(TAG, "所有示例运行完成")
    }
}
