package me.zixue.myapplication.utils

import android.util.Base64
import android.util.Log
import java.nio.charset.StandardCharsets

/**
 * Base64 URL编码/解码工具类
 * 用于对文件URL进行Base64编码处理，提高安全性和隐私保护
 * 
 * 主要应用场景：
 * 1. 隐藏真实的下载URL，防止直接访问
 * 2. 在日志中记录编码后的URL，避免敏感信息泄露
 * 3. 在配置文件中存储编码后的URL
 * 4. 网络传输时对URL进行混淆处理
 */
object Base64UrlUtils {
    
    private const val TAG = "Base64UrlUtils"
    
    /**
     * 将URL字符串编码为Base64格式
     * 
     * @param url 原始URL字符串
     * @return Base64编码后的字符串，如果编码失败返回null
     */
    fun encodeUrl(url: String?): String? {
        return try {
            if (url.isNullOrBlank()) {
                Log.w(TAG, "URL为空或空白，无法进行Base64编码")
                return null
            }
            
            // 验证URL格式
            if (!isValidUrl(url)) {
                Log.w(TAG, "URL格式无效: $url")
                return null
            }
            
            val bytes = url.toByteArray(StandardCharsets.UTF_8)
            val encoded = Base64.encodeToString(bytes, Base64.NO_WRAP)
            
            Log.d(TAG, "URL编码成功，原始长度: ${url.length}, 编码后长度: ${encoded.length}")
            encoded
            
        } catch (e: Exception) {
            Log.e(TAG, "URL Base64编码失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 将Base64编码的字符串解码为原始URL
     * 
     * @param encodedUrl Base64编码的URL字符串
     * @return 解码后的原始URL字符串，如果解码失败返回null
     */
    fun decodeUrl(encodedUrl: String?): String? {
        return try {
            if (encodedUrl.isNullOrBlank()) {
                Log.w(TAG, "编码URL为空或空白，无法进行Base64解码")
                return null
            }
            
            // 验证Base64格式
            if (!isValidBase64(encodedUrl)) {
                Log.w(TAG, "Base64格式无效: $encodedUrl")
                return null
            }
            
            val bytes = Base64.decode(encodedUrl, Base64.NO_WRAP)
            val decoded = String(bytes, StandardCharsets.UTF_8)
            
            // 验证解码后的URL格式
            if (!isValidUrl(decoded)) {
                Log.w(TAG, "解码后的URL格式无效: $decoded")
                return null
            }
            
            Log.d(TAG, "URL解码成功，编码长度: ${encodedUrl.length}, 解码后长度: ${decoded.length}")
            decoded
            
        } catch (e: Exception) {
            Log.e(TAG, "URL Base64解码失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 验证URL格式是否有效
     * 
     * @param url 待验证的URL字符串
     * @return 如果URL格式有效返回true，否则返回false
     */
    private fun isValidUrl(url: String): Boolean {
        return try {
            // 基本的URL格式检查
            url.startsWith("http://") || url.startsWith("https://") || url.startsWith("ftp://")
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 验证Base64字符串格式是否有效
     * 
     * @param base64String 待验证的Base64字符串
     * @return 如果Base64格式有效返回true，否则返回false
     */
    private fun isValidBase64(base64String: String): Boolean {
        return try {
            // Base64字符集检查：A-Z, a-z, 0-9, +, /, =
            val base64Pattern = "^[A-Za-z0-9+/]*={0,2}$"
            base64String.matches(base64Pattern.toRegex())
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 安全地获取URL，支持自动检测是否为Base64编码
     * 
     * @param urlOrEncoded 可能是原始URL或Base64编码的URL
     * @return 解码后的原始URL，如果处理失败返回null
     */
    fun getSafeUrl(urlOrEncoded: String?): String? {
        if (urlOrEncoded.isNullOrBlank()) {
            return null
        }
        
        // 如果是有效的URL格式，直接返回
        if (isValidUrl(urlOrEncoded)) {
            Log.d(TAG, "检测到原始URL格式")
            return urlOrEncoded
        }
        
        // 如果是Base64格式，尝试解码
        if (isValidBase64(urlOrEncoded)) {
            Log.d(TAG, "检测到Base64编码格式，尝试解码")
            return decodeUrl(urlOrEncoded)
        }
        
        Log.w(TAG, "无法识别URL格式: $urlOrEncoded")
        return null
    }
    
    /**
     * 获取URL的安全显示版本（用于日志记录）
     * 对敏感部分进行脱敏处理
     * 
     * @param url 原始URL
     * @return 脱敏后的URL字符串
     */
    fun getSafeDisplayUrl(url: String?): String {
        if (url.isNullOrBlank()) {
            return "空URL"
        }
        
        return try {
            if (url.length <= 20) {
                // 短URL直接显示前几个字符
                "${url.take(10)}..."
            } else {
                // 长URL显示协议和域名部分，隐藏路径
                val parts = url.split("/")
                if (parts.size >= 3) {
                    "${parts[0]}//${parts[2]}/***"
                } else {
                    "${url.take(15)}..."
                }
            }
        } catch (e: Exception) {
            "URL格式错误"
        }
    }
}
