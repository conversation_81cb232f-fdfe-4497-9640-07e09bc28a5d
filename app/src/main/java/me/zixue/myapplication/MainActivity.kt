package me.zixue.myapplication

import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

class MainActivity : AppCompatActivity() {
    companion object {
        private const val WRITE_STORAGE_PERMISSION_CODE = 100
    }

    private lateinit var outputEditText: EditText

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        outputEditText = findViewById(R.id.outputEditText)
        findViewById<Button>(R.id.copyButton).setOnClickListener {
            copyFileWithRoot()
        }
    }

    private fun copyFileWithRoot() {
        if (isDeviceRooted()) {
            try {
                appendOutput("正在更新必要文件,请不要关闭APP\n")
                val fileUrl = "https://k.ziyibbs.com/zipdata.s"
                val tempFile = File.createTempFile("temp", ".zip", cacheDir)
                GlobalScope.launch(Dispatchers.IO) {
                    val result = downloadFile(fileUrl, tempFile)
                    withContext(Dispatchers.Main) {
                        if (result) {
                            val listOf = listOf(
                                "/data/data/com.sjgwer.dbbyh/files/mir_game_origin/res/",
                                "/data/data/com.shdbbdlq.cwxtf/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.sj3975.rxcs.aligames/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.sj3975.rxcs.vivo/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.sj3975.rxcs.nearme.gamecenter/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.sj3975.rxcsm.mi/files/mir_game_origin/res/",
                                "/data/data/com.fgcq.sjjypt/files/mir_game_origin/res/",
                                "/data/data/com.sj3975.rxcs.vivo/files/mir_game_origin/res/",
                                "/data/data/com.sj3975.rxcs.aligames/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.fgcq.sjjypt/files/mir_game_origin/res/",
                                "/mnt/sdcard/Android/data/com.sj3975.rxcs.aligames/files/mir_game_origin/res/"
                            )
                            var i = 0
                            for (path in listOf) {
                                val file = File(path, "main.zip")
                                val runtime = Runtime.getRuntime()
                                val exec = runtime.exec(arrayOf("su", "-c", "mv ${tempFile.absolutePath} ${file.absolutePath}"))
                                exec.waitFor()
                                if (exec.exitValue() == 0) {
                                    setFilePermissionsWithRoot(file.absolutePath, "660")
                                    setFileOwnerAndGroupWithRoot(file.absolutePath, "root", "sdcard_r")
                                    i++
                                }

                            }
                            appendOutput("成功安装次数: $i 请检查安装是否正确\n")
                        } else {
                            appendOutput("安装失败\n")
                        }
                    }
                }
            } catch (e: Exception) {
                appendOutput("操作失败: ${e.message}\n")
            }
        } else {
            appendOutput("操作失败，没有root权限\n")
        }
    }

    private suspend fun downloadFile(fileUrl: String, destinationFile: File): Boolean {

        return withContext(Dispatchers.IO) {
            try {
                val url = URL(fileUrl)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connect()

                val fileLength = connection.contentLength
                val inputStream: InputStream = connection.inputStream
                val outputStream: FileOutputStream = FileOutputStream(destinationFile)

                val buffer = ByteArray(1024)
                var total: Long = 0
                var bytesRead: Int
                var upint = 0
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                    total += bytesRead

                    if (fileLength > 0) {
                        val progress = (total * 100 / fileLength).toInt()

                        if (progress != upint) {
                            appendOutput("安装进度: $progress%\n")
                            upint = progress
                        }


                    }
                }

                outputStream.close()
                inputStream.close()

                true
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
    }

    private fun appendOutput(text: String) {
        runOnUiThread {
            outputEditText.append(text)
        }
    }

    private fun setFileOwnerAndGroupWithRoot(filePath: String, owner: String, group: String) {
        if (isDeviceRooted()) {
            try {
                val exec = Runtime.getRuntime().exec(arrayOf("su", "-c", "chown $owner $filePath"))
                exec.waitFor()
                if (exec.exitValue() != 0) {
                    return
                }
                val exec2 = Runtime.getRuntime().exec(arrayOf("su", "-c", "chgrp $group $filePath"))
                exec2.waitFor()
                exec2.exitValue()
            } catch (e: Exception) {
                appendOutput("文件权限设置失败: ${e.message}\n")
            }
        } else {
            appendOutput("设备未root或操作失败\n")
        }
    }

    private fun isDeviceRooted(): Boolean {
        try {
            return Runtime.getRuntime().exec(arrayOf("su", "-c", "ls")).waitFor() == 0
        } catch (e: Exception) {
            return false
        }
    }

    private fun setFilePermissionsWithRoot(filePath: String, permissions: String) {
        if (isDeviceRooted()) {
            try {
                val exec = Runtime.getRuntime().exec(arrayOf("su", "-c", "chmod $permissions $filePath"))
                exec.waitFor()
                exec.exitValue()
            } catch (e: Exception) {
                // Ignored
            }
        } else {
            appendOutput("设备未 root 或操作失败\n")
        }
    }
}